#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Community Plan 切换功能
"""

import sys
import json
from team_manager import Config, APIClient

def test_community_plan_api():
    """测试 Community Plan API 调用"""
    print("🧪 测试 Community Plan 切换功能")
    print("=" * 50)
    
    # 创建配置和API客户端
    config = Config()
    api_client = APIClient(config)
    
    # 显示当前配置的API基础URL
    base_url = config.get('api.base_url', 'N/A')
    print(f"📡 API基础URL: {base_url}")
    
    # 显示请求头信息（隐藏敏感信息）
    headers = api_client._get_headers()
    safe_headers = {}
    for key, value in headers.items():
        if key.lower() == 'cookie':
            safe_headers[key] = f"{value[:50]}..." if len(value) > 50 else value
        else:
            safe_headers[key] = value
    
    print(f"📋 请求头: {json.dumps(safe_headers, indent=2, ensure_ascii=False)}")
    
    # 测试API调用（但不实际执行，只显示请求信息）
    print("\n🔄 准备测试 Community Plan 切换...")
    print(f"📍 目标URL: {base_url}/put-user-on-plan")
    print(f"📦 请求数据: {json.dumps({'planId': 'orb_community_plan'}, indent=2)}")
    
    # 询问用户是否要实际执行
    print("\n⚠️  注意：这将实际调用API并可能改变您的账号计划！")
    user_input = input("是否要实际执行API调用？(y/N): ").strip().lower()
    
    if user_input == 'y':
        print("\n🚀 执行API调用...")
        try:
            success, message = api_client.put_user_on_community_plan()
            if success:
                print(f"✅ 成功: {message}")
            else:
                print(f"❌ 失败: {message}")
        except Exception as e:
            print(f"💥 异常: {str(e)}")
    else:
        print("\n🛑 用户取消了API调用")
    
    print("\n✨ 测试完成！")

if __name__ == "__main__":
    test_community_plan_api()
