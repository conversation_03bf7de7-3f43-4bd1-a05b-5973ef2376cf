#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队管理工具启动脚本
Team Manager Launcher Script
"""

import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True


def check_dependencies():
    """检查依赖项"""
    required_packages = ['PyQt6', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    return missing_packages


def install_dependencies(missing_packages):
    """安装缺失的依赖项"""
    if not missing_packages:
        return True
    
    print(f"\n📦 正在安装缺失的依赖项: {', '.join(missing_packages)}")
    
    try:
        # 尝试使用pip安装
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '--upgrade'
        ] + missing_packages)
        print("✅ 依赖项安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖项安装失败: {e}")
        print("\n请手动安装依赖项:")
        print(f"pip install {' '.join(missing_packages)}")
        return False


def check_config_file():
    """检查配置文件"""
    config_file = Path("team_manager_config.json")
    if config_file.exists():
        print("✅ 配置文件已存在")
        return True
    else:
        print("ℹ️  配置文件不存在，将在首次运行时创建")
        return True


def run_team_manager():
    """运行团队管理工具"""
    try:
        print("\n🚀 启动团队管理工具...")
        
        # 导入并运行主程序
        from team_manager import main
        main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 team_manager.py 文件在当前目录中")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("=" * 50)
    print("🛠️  团队管理工具启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("\n按回车键退出...")
        sys.exit(1)
    
    # 检查依赖项
    missing_packages = check_dependencies()
    
    # 如果有缺失的依赖项，尝试安装
    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖项: {', '.join(missing_packages)}")
        response = input("是否自动安装? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            if not install_dependencies(missing_packages):
                input("\n按回车键退出...")
                sys.exit(1)
        else:
            print("\n请手动安装依赖项后再运行:")
            print(f"pip install {' '.join(missing_packages)}")
            input("\n按回车键退出...")
            sys.exit(1)
    
    # 检查配置文件
    check_config_file()
    
    # 运行主程序
    print("\n" + "=" * 50)
    if not run_team_manager():
        input("\n按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动器错误: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
