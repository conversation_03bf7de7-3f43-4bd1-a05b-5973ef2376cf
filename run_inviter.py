#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例：如何使用团队邀请工具包
"""

import sys
from team_inviter import TeamInviter


def main():
    """主函数"""
    print("=" * 50)
    print("团队邀请工具使用示例")
    print("=" * 50)
    
    # 创建TeamInviter实例
    inviter = TeamInviter()
    
    # 1. 设置API配置
    # 注意：实际使用时需要设置真实的cookie和base_url
    inviter.set_base_url("https://app.augmentcode.com/api")
    # inviter.set_cookie("your_cookie_here")  # 取消注释并设置实际cookie
    
    # 2. 验证邮箱
    print("\n[验证邮箱示例]")
    emails_text = """
    <EMAIL>
    invalid_email
    <EMAIL>
    """
    valid_emails, invalid_emails = inviter.validate_emails(emails_text)
    print(f"有效邮箱: {valid_emails}")
    print(f"无效邮箱: {invalid_emails}")
    
    # 3. 获取团队数据
    print("\n[获取团队数据示例]")
    print("注意：此操作需要设置有效的Cookie才能成功")
    success, data = inviter.get_team_data()
    if success:
        print("团队数据获取成功")
        
        # 4. 获取待处理邀请
        invitations = inviter.get_pending_invitations()
        print(f"找到 {len(invitations)} 个待处理邀请")
        
        # 5. 获取待处理邀请的邮箱
        pending_emails = inviter.get_pending_emails()
        print(f"待处理邀请邮箱: {pending_emails}")
    else:
        print(f"获取团队数据失败: {data}")
    
    # 6. 邀请成员
    print("\n[邀请成员示例]")
    print("注意：此操作需要设置有效的Cookie才能成功")
    # valid_emails已经在上面验证邮箱步骤中获取
    if valid_emails:
        print(f"准备邀请 {len(valid_emails)} 个邮箱...")
        # 注释以下代码以避免实际发送邀请
        # success, message = inviter.invite_members(valid_emails)
        # print(f"邀请结果: {'成功' if success else '失败'}")
        # print(f"消息: {message}")
    
    print("\n[批量删除示例]")
    print("注意：删除操作不可撤销，此处仅作示例说明")
    print("示例: inviter.batch_delete_invitations(invitation_ids)")
    
    print("\n=" * 25)
    print("要了解更多功能，请查看README.md文件")
    print("可以使用以下命令启动图形界面或命令行界面:")
    print("  python -m team_inviter         # 图形界面")
    print("  python -m team_inviter --cli   # 命令行界面")
    print("=" * 50)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"程序出错: {e}")
        sys.exit(1) 