import streamlit as st
import requests
import pandas as pd
import time
import re

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def invite_members(emails):
    """批量邀请成员"""
    url = "https://app.augmentcode.com/api/team/invite"
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": "_ga=GA1.1.**********.**********; vector_visitor_id=16cd1864-ab50-42c7-9906-0c9e4db2574d; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201973d97-58b1-7263-bfe3-cd1a606c05e4%22%2C%22%24sesid%22%3A%5B1749438152506%2C%22019752a3-bf26-7e4c-912e-231f39529e65%22%2C1749438152486%5D%7D; _ga_J5WQ9TVV7R=GS2.1.s1749438152$o2$g0$t1749438154$j58$l0$h0; _gcl_au=1.1.252217321.**********.1202800900.1749602686.1749602714; _session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D.Ei68rUOGZUIeRDrCtA0U2krvadz1a1GZRnIlESPHa6U; ajs_user_id=3c2c213f-7c58-4212-935d-************; ajs_anonymous_id=cc44fa44-b50c-49db-9635-e3262307411b; _ga_F6GPDJDCJY=GS2.1.s1749602657$o22$g1$t1749604609$j60$l0$h0; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%223c2c213f-7c58-4212-935d-************%22%2C%22%24sesid%22%3A%5B1749604609877%2C%2201975c75-3ff7-76b1-b059-7f3c41a69adc%22%2C1749602877431%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Faccounts.google.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fselect-plan%22%7D%7D",
        "Referer": "https://app.augmentcode.com/account/team",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }

    # 构建请求体
    data = {"emails": emails}

    try:
        response = requests.post(url, headers=headers, json=data)
        return response.status_code == 200, response.text
    except Exception as e:
        return False, str(e)

def get_team_data():
    """获取团队数据"""
    url = "https://app.augmentcode.com/api/team"
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": "_ga=GA1.1.**********.**********; vector_visitor_id=16cd1864-ab50-42c7-9906-0c9e4db2574d; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201973d97-58b1-7263-bfe3-cd1a606c05e4%22%2C%22%24sesid%22%3A%5B1749438152506%2C%22019752a3-bf26-7e4c-912e-231f39529e65%22%2C1749438152486%5D%7D; _ga_J5WQ9TVV7R=GS2.1.s1749438152$o2$g0$t1749438154$j58$l0$h0; _gcl_au=1.1.252217321.**********.1202800900.1749602686.1749602714; _session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D.Ei68rUOGZUIeRDrCtA0U2krvadz1a1GZRnIlESPHa6U; ajs_user_id=3c2c213f-7c58-4212-935d-************; ajs_anonymous_id=cc44fa44-b50c-49db-9635-e3262307411b; _ga_F6GPDJDCJY=GS2.1.s1749602657$o22$g1$t1749604609$j60$l0$h0; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%223c2c213f-7c58-4212-935d-************%22%2C%22%24sesid%22%3A%5B1749604609877%2C%2201975c75-3ff7-76b1-b059-7f3c41a69adc%22%2C1749602877431%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Faccounts.google.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fselect-plan%22%7D%7D",
        "Referer": "https://app.augmentcode.com/account/team",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        return None

st.set_page_config(page_title="团队用户信息", page_icon=":busts_in_silhouette:", layout="wide")

# 初始化session state
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'team_data' not in st.session_state:
    st.session_state.team_data = None
if 'batch_delete_running' not in st.session_state:
    st.session_state.batch_delete_running = False

st.title("👥 团队用户信息展示")
st.write("管理团队成员：邀请新成员、查看现有成员、批量删除等操作。")

# 邀请功能区域
st.markdown("## 📧 邀请新成员")
st.write("在下方文本框中输入邮箱地址，每行一个邮箱，然后点击批量邀请按钮。")

# 邮箱输入区域
email_input = st.text_area(
    "邮箱地址（每行一个）",
    placeholder="<EMAIL>\<EMAIL>\<EMAIL>",
    height=150,
    help="请输入要邀请的邮箱地址，每行一个邮箱"
)

# 实时验证显示
if email_input.strip():
    raw_emails = [email.strip() for email in email_input.strip().split('\n') if email.strip()]
    if raw_emails:
        valid_count = sum(1 for email in raw_emails if validate_email(email))
        invalid_count = len(raw_emails) - valid_count

        col_info1, col_info2, col_info3 = st.columns(3)
        with col_info1:
            st.metric("总邮箱数", len(raw_emails))
        with col_info2:
            st.metric("有效邮箱", valid_count, delta=None if valid_count == 0 else "✅")
        with col_info3:
            st.metric("无效邮箱", invalid_count, delta=None if invalid_count == 0 else "❌")

# 邀请按钮和处理逻辑
col1, col2, col3 = st.columns([1, 1, 2])
with col1:
    if st.button("📨 批量邀请", key="batch_invite"):
        if email_input.strip():
            # 解析邮箱列表
            raw_emails = [email.strip() for email in email_input.strip().split('\n') if email.strip()]

            if raw_emails:
                # 验证邮箱格式
                valid_emails = []
                invalid_emails = []

                for email in raw_emails:
                    if validate_email(email):
                        valid_emails.append(email)
                    else:
                        invalid_emails.append(email)

                # 显示验证结果
                if invalid_emails:
                    st.error(f"❌ 发现 {len(invalid_emails)} 个无效邮箱格式:")
                    for email in invalid_emails:
                        st.write(f"  • {email}")

                if valid_emails:
                    st.write(f"✅ 准备邀请 {len(valid_emails)} 个有效邮箱:")
                    for email in valid_emails:
                        st.write(f"  • {email}")

                    with st.spinner("正在发送邀请..."):
                        success, response = invite_members(valid_emails)

                    if success:
                        st.success(f"🎉 成功发送邀请给 {len(valid_emails)} 个邮箱！")
                        # 自动刷新数据
                        if st.session_state.data_loaded:
                            st.session_state.team_data = get_team_data()
                            st.rerun()
                    else:
                        st.error(f"❌ 邀请发送失败: {response}")
                else:
                    st.warning("没有找到有效的邮箱地址")
            else:
                st.warning("请输入至少一个邮箱地址")
        else:
            st.warning("请输入邮箱地址")

with col2:
    if st.button("🧹 清空输入", key="clear_input"):
        st.rerun()

st.markdown("---")  # 分隔线



def delete_member(member_id):
    """删除单个成员或邀请"""
    url = f"https://app.augmentcode.com/api/team/invite/{member_id}"
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": "_ga=GA1.1.**********.**********; vector_visitor_id=16cd1864-ab50-42c7-9906-0c9e4db2574d; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201973d97-58b1-7263-bfe3-cd1a606c05e4%22%2C%22%24sesid%22%3A%5B1749438152506%2C%22019752a3-bf26-7e4c-912e-231f39529e65%22%2C1749438152486%5D%7D; _ga_J5WQ9TVV7R=GS2.1.s1749438152$o2$g0$t1749438154$j58$l0$h0; _gcl_au=1.1.252217321.**********.1202800900.1749602686.1749602714; _session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D.Ei68rUOGZUIeRDrCtA0U2krvadz1a1GZRnIlESPHa6U; ajs_user_id=3c2c213f-7c58-4212-935d-************; ajs_anonymous_id=cc44fa44-b50c-49db-9635-e3262307411b; _ga_F6GPDJDCJY=GS2.1.s1749602657$o22$g1$t1749604609$j60$l0$h0; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%223c2c213f-7c58-4212-935d-************%22%2C%22%24sesid%22%3A%5B1749604609877%2C%2201975c75-3ff7-76b1-b059-7f3c41a69adc%22%2C1749602877431%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Faccounts.google.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fselect-plan%22%7D%7D",
        "Referer": "https://app.augmentcode.com/account/team",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    try:
        response = requests.delete(url, headers=headers)
        return response.status_code == 200
    except Exception as e:
        return False



def batch_delete_invitations(invitation_ids):
    """批量删除邀请记录"""
    success_count = 0
    failed_count = 0
    failed_ids = []

    # 创建进度条
    progress_bar = st.progress(0)
    status_text = st.empty()

    total = len(invitation_ids)
    st.write(f"开始批量删除 {total} 条邀请记录...")

    for i, invitation_id in enumerate(invitation_ids):
        status_text.text(f"正在删除第 {i+1}/{total} 条邀请记录 (ID: {invitation_id})...")
        progress_bar.progress((i + 1) / total)

        # 添加调试信息
        st.write(f"正在删除邀请ID: {invitation_id}")

        try:
            result = delete_member(invitation_id)
            if result:
                success_count += 1
                st.write(f"✅ 成功删除邀请ID: {invitation_id}")
            else:
                failed_count += 1
                failed_ids.append(invitation_id)
                st.write(f"❌ 删除失败邀请ID: {invitation_id}")
        except Exception as e:
            failed_count += 1
            failed_ids.append(invitation_id)
            st.write(f"❌ 删除异常邀请ID: {invitation_id}, 错误: {str(e)}")

    # 清除进度条和状态文本
    progress_bar.empty()
    status_text.empty()

    return success_count, failed_count, failed_ids

def batch_delete_members(member_ids):
    """批量删除未加入成员"""
    success_count = 0
    failed_count = 0
    failed_ids = []

    # 创建进度条
    progress_bar = st.progress(0)
    status_text = st.empty()

    total = len(member_ids)
    st.write(f"开始批量删除 {total} 个未加入成员...")

    for i, member_id in enumerate(member_ids):
        status_text.text(f"正在删除第 {i+1}/{total} 个未加入成员 (ID: {member_id})...")
        progress_bar.progress((i + 1) / total)

        # 添加调试信息
        st.write(f"正在删除成员ID: {member_id}")

        try:
            result = delete_member(member_id)
            if result:
                success_count += 1
                st.write(f"✅ 成功删除成员ID: {member_id}")
            else:
                failed_count += 1
                failed_ids.append(member_id)
                st.write(f"❌ 删除失败成员ID: {member_id}")
        except Exception as e:
            failed_count += 1
            failed_ids.append(member_id)
            st.write(f"❌ 删除异常成员ID: {member_id}, 错误: {str(e)}")

    # 清除进度条和状态文本
    progress_bar.empty()
    status_text.empty()

    return success_count, failed_count, failed_ids

# 数据管理区域
st.markdown("## 👥 团队成员管理")

# 数据加载按钮
if st.button("🔄 获取团队用户信息"):
    with st.spinner("正在加载数据，请稍候..."):
        st.session_state.team_data = get_team_data()
        st.session_state.data_loaded = True

# 显示数据（如果已加载）
if st.session_state.data_loaded and st.session_state.team_data:
    data = st.session_state.team_data
    users = []
    if data:
        if isinstance(data, list):
            users = data
        elif isinstance(data, dict):
            # 递归提取所有嵌套的用户列表
            def extract_users(obj):
                result = []
                if isinstance(obj, dict):
                    for k, v in obj.items():
                        if k == "users" and isinstance(v, list):
                            result.extend(v)
                        else:
                            result.extend(extract_users(v))
                elif isinstance(obj, list):
                    for item in obj:
                        result.extend(extract_users(item))
                return result
            users = extract_users(data)
    if users:
        df_users = pd.DataFrame(users)
        show_fields_users = ["id", "email", "role", "joinedAt"]
        for field in show_fields_users:
            if field not in df_users.columns:
                df_users[field] = None
        df_users = df_users[show_fields_users]
        df_users.insert(0, "序号", range(1, len(df_users) + 1))
        st.markdown("### 团队成员（users）")
        st.dataframe(df_users, use_container_width=True)

        # 提取 invitations
        invitations = []
        if data:
            if isinstance(data, dict):
                if "invitations" in data:
                    invitations = data["invitations"]
                elif "team" in data and "invitations" in data["team"]:
                    invitations = data["team"]["invitations"]

        if invitations:
            df_inv = pd.DataFrame(invitations)
            show_fields_inv = ["id", "email", "invitedAt"]
            for field in show_fields_inv:
                if field not in df_inv.columns:
                    df_inv[field] = None
            df_inv = df_inv[show_fields_inv]
            df_inv.insert(0, "序号", range(1, len(df_inv) + 1))
            st.markdown("### 邀请记录（invitations）")

            # 批量删除按钮
            ids_to_delete_inv = df_inv["id"].tolist()
            st.write(f"找到 {len(ids_to_delete_inv)} 条邀请记录可以删除")
            st.write(f"邀请记录ID列表: {ids_to_delete_inv}")

            # 批量删除邀请记录
            col1, col2 = st.columns([1, 1])
            with col1:
                if st.button("删除所有邀请记录（批量删除）", key="batch_delete_inv", disabled=len(ids_to_delete_inv) == 0):
                    # 创建一个容器来显示删除进度
                    progress_container = st.container()

                    with progress_container:
                        st.write("🚀 开始执行批量删除...")

                        # 创建进度条
                        progress_bar = st.progress(0)
                        status_text = st.empty()

                        success_count = 0
                        failed_count = 0
                        failed_ids = []
                        total = len(ids_to_delete_inv)

                        for i, invitation_id in enumerate(ids_to_delete_inv):
                            status_text.text(f"正在删除第 {i+1}/{total} 条邀请记录 (ID: {invitation_id})")
                            progress_bar.progress((i + 1) / total)

                            try:
                                if delete_member(invitation_id):
                                    success_count += 1
                                else:
                                    failed_count += 1
                                    failed_ids.append(invitation_id)
                            except Exception as e:
                                failed_count += 1
                                failed_ids.append(invitation_id)
                                st.error(f"删除异常: {invitation_id}, 错误: {str(e)}")

                        # 清除进度显示
                        progress_bar.empty()
                        status_text.empty()

                        # 显示结果
                        if failed_count == 0:
                            st.success(f"✅ 成功删除所有 {success_count} 条邀请记录！")
                        else:
                            st.warning(f"⚠️ 删除完成：成功 {success_count} 条，失败 {failed_count} 条")
                            if failed_ids:
                                st.error(f"删除失败的ID: {', '.join(failed_ids)}")

                        # 自动刷新数据
                        time.sleep(1)
                        st.session_state.team_data = get_team_data()
                        st.rerun()

            with col2:
                if st.button("刷新数据", key="refresh_inv"):
                    st.session_state.team_data = get_team_data()
                    st.rerun()

            # 表格展示（带单个删除按钮）
            columns_inv = st.columns(len(df_inv.columns))
            for i, field in enumerate(df_inv.columns):
                columns_inv[i].write(field)
            for idx, row in df_inv.iterrows():
                cols_inv = st.columns(len(df_inv.columns))
                for i, field in enumerate(df_inv.columns):
                    if field == "invitedAt":
                        if cols_inv[i].button("删除", key=f"del_inv_{row['id']}"):
                            if delete_member(row["id"]):
                                st.success(f"已删除邀请 {row['email']}，请重新加载数据。")
                            else:
                                st.error(f"删除失败：{row['email']}")
                        else:
                            cols_inv[i].write(row[field])
                    else:
                        cols_inv[i].write(row[field])

        # 批量删除按钮
        ids_to_delete = df_users[df_users["role"].isnull()]["id"].tolist()
        st.write(f"找到 {len(ids_to_delete)} 个未加入成员可以删除")
        st.write(f"未加入成员ID列表: {ids_to_delete}")

        # 批量删除未加入成员
        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("删除未加入成员（批量删除）", disabled=len(ids_to_delete) == 0):
                # 创建一个容器来显示删除进度
                progress_container = st.container()

                with progress_container:
                    st.write("🚀 开始执行批量删除...")

                    # 创建进度条
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    success_count = 0
                    failed_count = 0
                    failed_ids = []
                    total = len(ids_to_delete)

                    for i, member_id in enumerate(ids_to_delete):
                        status_text.text(f"正在删除第 {i+1}/{total} 个未加入成员 (ID: {member_id})")
                        progress_bar.progress((i + 1) / total)

                        try:
                            if delete_member(member_id):
                                success_count += 1
                            else:
                                failed_count += 1
                                failed_ids.append(member_id)
                        except Exception as e:
                            failed_count += 1
                            failed_ids.append(member_id)
                            st.error(f"删除异常: {member_id}, 错误: {str(e)}")

                    # 清除进度显示
                    progress_bar.empty()
                    status_text.empty()

                    # 显示结果
                    if failed_count == 0:
                        st.success(f"✅ 成功删除所有 {success_count} 个未加入成员！")
                    else:
                        st.warning(f"⚠️ 删除完成：成功 {success_count} 个，失败 {failed_count} 个")
                        if failed_ids:
                            st.error(f"删除失败的ID: {', '.join(failed_ids)}")

                    # 自动刷新数据
                    time.sleep(1)
                    st.session_state.team_data = get_team_data()
                    st.rerun()

        with col2:
            if st.button("刷新数据", key="refresh_members"):
                st.session_state.team_data = get_team_data()
                st.rerun()

        # 表格展示（带单个删除按钮）
        columns = st.columns(len(df_users.columns))
        for i, field in enumerate(df_users.columns):
            columns[i].write(field)
        for idx, row in df_users.iterrows():
            cols = st.columns(len(df_users.columns))
            for i, field in enumerate(df_users.columns):
                if field == "role" and pd.isnull(row["role"]):
                    if cols[i].button("删除", key=f"del_{row['id']}"):
                        if delete_member(row["id"]):
                            st.success(f"已删除成员 {row['email']}，请重新加载数据。")
                        else:
                            st.error(f"删除失败：{row['email']}")
                    else:
                        cols[i].write("未加入成员")
                else:
                    cols[i].write(row[field])
    else:
        st.warning("未获取到数据，请检查接口或网络。")

# 添加清除数据按钮
if st.session_state.data_loaded:
    if st.button("清除数据", key="clear_data"):
        st.session_state.data_loaded = False
        st.session_state.team_data = None
        st.rerun()

if not st.session_state.data_loaded:
    st.info("请点击上方按钮加载团队用户信息。")