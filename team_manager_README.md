# 团队管理工具 v1.0

一个功能强大的团队成员管理工具，基于 PyQt6 开发，支持邀请成员、查看团队信息、批量删除操作等功能。

## ✨ 主要功能

### 📧 邀请成员
- 批量邀请多个邮箱地址
- 实时邮箱格式验证
- 邀请历史记录
- 智能错误处理

### 👥 团队管理
- 查看团队成员信息
- 查看邀请记录
- 实时数据刷新
- 自动数据同步

### 🔄 批量操作
- 批量删除未加入成员
- 批量删除邀请记录
- 批量删除所有未确认项目
- 操作进度显示

### 📊 数据管理
- 原始数据查看
- 数据导出功能
- JSON格式化显示
- 配置文件管理

### ⚙️ 配置管理
- 灵活的配置系统
- 本地配置文件保存
- 配置导入/导出
- 实时配置更新

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- PyQt6
- requests

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python team_manager.py
```

## 📋 配置说明

### 首次使用
1. 运行应用后，点击菜单栏的 "工具" -> "配置设置"
2. 在 "API配置" 标签页中配置以下信息：
   - **Cookie**: 从浏览器开发者工具中获取的完整 cookie 字符串
   - **User Agent**: 浏览器用户代理字符串
   - **Referer**: 引用页面URL

### 获取 Cookie 信息
1. 打开浏览器，访问 `https://app.augmentcode.com/account/team`
2. 按 F12 打开开发者工具
3. 切换到 "Network" 标签页
4. 刷新页面或执行一个API请求
5. 找到对应的API请求，查看请求头
6. 复制完整的 Cookie 字符串到配置中

### 配置文件
应用会自动在当前目录创建 `team_manager_config.json` 配置文件，包含：
- API配置（URL、请求头等）
- UI配置（主题、字体大小等）
- 功能配置（自动刷新、批量操作等）

## 🎯 使用指南

### 邀请新成员
1. 切换到 "📧 邀请成员" 标签页
2. 在文本框中输入邮箱地址，每行一个
3. 系统会实时验证邮箱格式
4. 点击 "📨 批量邀请" 按钮发送邀请
5. 查看邀请历史记录

### 管理团队成员
1. 切换到 "👥 团队管理" 标签页
2. 点击 "🔄 获取团队数据" 加载数据
3. 查看团队成员和邀请记录表格
4. 使用 "🔃 刷新" 按钮更新数据

### 批量删除操作
1. 切换到 "🔄 批量操作" 标签页
2. 确保已加载团队数据
3. 选择相应的批量删除操作：
   - 删除未加入成员
   - 删除邀请记录
   - 删除所有未确认项目
4. 确认操作并查看进度
5. 查看操作日志

### 数据查看和导出
1. 切换到 "📊 数据查看" 标签页
2. 查看原始JSON数据
3. 使用 "🎨 格式化显示" 美化数据
4. 使用 "📤 导出数据" 保存数据到文件

## ⚙️ 高级功能

### 自动刷新
- 在配置中启用自动刷新功能
- 设置刷新间隔（10-300秒）
- 自动保持数据最新

### 配置管理
- 导入/导出配置文件
- 多环境配置切换
- 配置备份和恢复

### 主题和界面
- 支持明暗主题切换
- 可调节字体大小
- 响应式界面布局

## 🛡️ 安全注意事项

1. **Cookie 安全**: Cookie 包含敏感的认证信息，请妥善保管
2. **配置文件**: 不要将包含 Cookie 的配置文件分享给他人
3. **网络安全**: 确保在安全的网络环境中使用
4. **权限控制**: 只有具有相应权限的用户才能执行删除操作

## 🔧 技术栈

- **Python 3.8+**: 主要编程语言
- **PyQt6**: GUI框架
- **Requests**: HTTP请求库
- **JSON**: 数据格式
- **Threading**: 多线程处理

## 📁 项目结构

```
team_manager/
├── team_manager.py              # 主应用程序
├── requirements.txt             # 依赖项列表
├── team_manager_README.md       # 说明文档
├── team_manager_config.json     # 配置文件（自动生成）
└── exported_data/               # 导出数据目录（可选）
```

## 🐛 故障排除

### 常见问题

1. **无法连接API**
   - 检查网络连接
   - 验证Cookie是否有效
   - 确认API URL正确

2. **邀请失败**
   - 检查邮箱格式
   - 验证用户权限
   - 查看错误信息

3. **界面显示异常**
   - 检查PyQt6安装
   - 重置配置文件
   - 重启应用程序

### 日志和调试
- 查看控制台输出
- 检查操作日志
- 导出配置文件进行分析

## 📝 更新日志

### v1.0 (2024-01-XX)
- 初始版本发布
- 支持基本的团队管理功能
- 实现配置管理系统
- 添加批量操作功能

## 🤝 贡献

欢迎提交问题报告和功能请求！

## 📄 许可证

本项目采用 MIT 许可证。
