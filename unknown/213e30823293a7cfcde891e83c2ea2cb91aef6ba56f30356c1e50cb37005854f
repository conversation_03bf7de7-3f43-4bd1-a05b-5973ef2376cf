#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统计卡片更新功能
"""

import sys
import json

def test_stats_logic():
    """测试统计逻辑"""
    print("🧪 测试统计卡片更新逻辑")
    print("=" * 40)
    
    # 模拟用户数据
    users = [
        {"id": "1", "email": "<EMAIL>", "role": "MEMBER"},
        {"id": "2", "email": "<EMAIL>", "role": "ADMIN"},
        {"id": "3", "email": "<EMAIL>", "role": ""},  # 未加入
        {"id": "4", "email": "<EMAIL>", "role": None},  # 未加入
    ]
    
    # 模拟邀请数据
    invitations = [
        {"id": "inv1", "email": "<EMAIL>", "invitedAt": 1703980800000},  # 2023-12-31
        {"id": "inv2", "email": "<EMAIL>", "invitedAt": 1704067200000},  # 2024-01-01
    ]
    
    print("📊 模拟数据:")
    print(f"  用户: {len(users)} 个")
    print(f"  邀请: {len(invitations)} 个")
    
    # 计算统计
    total_members = len(users)
    active_members = len([u for u in users if u.get('role') and u.get('role') != '未加入'])
    pending_members = total_members - active_members
    
    print(f"\n📈 统计结果:")
    print(f"  总成员: {total_members}")
    print(f"  已加入: {active_members}")
    print(f"  待加入: {pending_members}")
    
    # 计算今日邀请
    from datetime import datetime, date
    today = date.today()
    recent_invitations = 0
    
    for invitation in invitations:
        invited_at = invitation.get('invitedAt', '')
        if invited_at:
            try:
                if isinstance(invited_at, (int, float)):
                    invite_date = datetime.fromtimestamp(invited_at / 1000).date()
                    print(f"  邀请日期: {invite_date} (今日: {today})")
                    if invite_date == today:
                        recent_invitations += 1
            except Exception as e:
                print(f"  解析邀请时间失败: {e}")
    
    print(f"  总邀请: {len(invitations)}")
    print(f"  今日邀请: {recent_invitations}")
    
    print("\n✅ 统计逻辑测试完成")

def check_config():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        with open('team_manager_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件存在")
        
        # 检查API配置
        api_config = config.get('api', {})
        base_url = api_config.get('base_url', '')
        headers = api_config.get('headers', {})
        cookie = headers.get('cookie', '')
        
        print(f"📡 API地址: {base_url}")
        print(f"🍪 Cookie配置: {'已配置' if cookie else '未配置'}")
        
        if not cookie:
            print("⚠️  Cookie未配置，请在主程序中配置API设置")
            print("💡 步骤:")
            print("   1. 运行主程序: python team_manager.py")
            print("   2. 点击菜单: 工具 -> 配置设置")
            print("   3. 在API配置页面填入Cookie")
        
    except FileNotFoundError:
        print("❌ 配置文件不存在")
        print("💡 请先运行主程序并配置API设置")
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")

def main():
    """主函数"""
    print("🛠️  团队管理工具 - 统计功能测试")
    print("=" * 50)
    
    test_stats_logic()
    check_config()
    
    print("\n" + "=" * 50)
    print("🎯 修复说明:")
    print("1. ✅ 已修复统计卡片更新逻辑")
    print("2. ✅ 已添加标签引用保存机制")
    print("3. ✅ 已完善数据统计计算")
    print("\n💡 现在重新启动应用，统计卡片应该会正确显示数据！")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户取消")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        input("\n按回车键退出...")
