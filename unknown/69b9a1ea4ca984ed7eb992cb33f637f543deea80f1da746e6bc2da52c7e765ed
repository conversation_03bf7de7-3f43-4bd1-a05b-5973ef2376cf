#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的团队管理工具启动脚本
用于测试和调试
"""

import sys
import os

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    missing = []
    
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        missing.append("PyQt6")
        print("❌ PyQt6 未安装")
    
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        missing.append("requests")
        print("❌ requests 未安装")
    
    if missing:
        print(f"\n❌ 缺少依赖项: {', '.join(missing)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    print("✅ 所有依赖项已安装")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🛠️  团队管理工具 - 简化启动器")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n🚀 启动应用...")
    
    try:
        # 导入并运行主程序
        from team_manager import main as run_app
        run_app()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 team_manager.py 文件在当前目录中")
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试提供解决方案
        if "ApplicationAttribute" in str(e):
            print("\n💡 解决方案:")
            print("这可能是PyQt6版本兼容性问题")
            print("请尝试更新PyQt6: pip install --upgrade PyQt6")
        
        elif "QFont" in str(e):
            print("\n💡 解决方案:")
            print("字体设置问题，应用会使用默认字体")
        
        elif "QApplication" in str(e):
            print("\n💡 解决方案:")
            print("请确保您的系统支持GUI应用程序")
            print("如果是远程服务器，请确保X11转发已启用")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 启动器错误: {e}")
        input("\n按回车键退出...")
