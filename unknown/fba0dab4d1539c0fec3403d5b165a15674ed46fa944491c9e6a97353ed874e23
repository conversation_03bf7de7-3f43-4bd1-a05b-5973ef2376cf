#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试版本 - 用于验证PyQt6基本功能
"""

import sys

def test_pyqt6():
    """测试PyQt6基本功能"""
    try:
        print("🔍 测试PyQt6导入...")
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        print("✅ PyQt6导入成功")
        
        print("🔍 测试QApplication创建...")
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        print("🔍 测试窗口创建...")
        window = QMainWindow()
        window.setWindowTitle("PyQt6测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("🎉 PyQt6测试成功！")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("font-size: 18px; color: #007bff; padding: 20px;")
        
        # 添加按钮
        button = QPushButton("关闭")
        button.clicked.connect(window.close)
        button.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        
        layout.addWidget(label)
        layout.addWidget(button)
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        print("✅ 窗口创建成功")
        
        print("🚀 显示窗口...")
        window.show()
        
        print("✅ 所有测试通过！窗口已显示")
        print("💡 如果看到窗口，说明PyQt6工作正常")
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ PyQt6导入失败: {e}")
        print("请安装PyQt6: pip install PyQt6")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 PyQt6最小化测试")
    print("=" * 50)
    
    test_pyqt6()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户取消")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        input("\n按回车键退出...")
