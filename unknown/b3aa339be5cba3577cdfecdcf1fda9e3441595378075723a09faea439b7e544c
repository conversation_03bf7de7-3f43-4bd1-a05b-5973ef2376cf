#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队邀请工具 - 主入口
支持命令行和图形界面启动
"""

import sys
import os

def main():
    """主入口函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        # 如果有--cli参数，使用命令行界面
        from .cli import main as cli_main
        cli_main()
    else:
        # 默认使用图形界面
        try:
            from .gui import main as gui_main
            gui_main()
        except ImportError as e:
            print(f"错误: 无法启动图形界面 - {e}")
            print("提示: 使用 'pip install PyQt6' 安装图形界面所需依赖")
            print("或者使用 '--cli' 参数启动命令行界面")
            sys.exit(1)


if __name__ == "__main__":
    main() 