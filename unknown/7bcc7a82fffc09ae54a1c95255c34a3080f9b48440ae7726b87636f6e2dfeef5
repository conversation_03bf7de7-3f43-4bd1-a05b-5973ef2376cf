# Community Plan 切换功能说明

## 功能概述

已成功在团队管理工具中添加了将登录账号切换到 Community Plan 的功能。

## 实现详情

### 1. API 客户端方法
在 `APIClient` 类中添加了新方法：

```python
def put_user_on_community_plan(self) -> Tuple[bool, str]:
    """将登录账号改为 community plan"""
    try:
        url = f"{self.config.get('api.base_url')}/put-user-on-plan"
        headers = self._get_headers()
        data = {"planId": "orb_community_plan"}
        
        response = self.session.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            return True, "账号已成功切换到 Community Plan"
        else:
            return False, f"切换失败，状态码: {response.status_code}\n响应: {response.text}"
    except Exception as e:
        return False, f"网络错误: {str(e)}"
```

### 2. 异步支持
在 `WorkerThread` 类中添加了对应的操作支持：

```python
elif self.operation == "put_user_on_community_plan":
    success, message = self.api_client.put_user_on_community_plan()
    self.finished.emit(success, message, None)
```

### 3. 用户界面
在"批量操作"标签页中添加了新按钮：

- **按钮标题**: "🔄 切换到 Community Plan"
- **按钮描述**: "将当前登录账号切换到 Community Plan"
- **按钮颜色**: 绿色渐变 (#28a745 到 #20c997)
- **位置**: 批量操作网格的第2行第2列

### 4. 事件处理
添加了对应的事件处理方法：

```python
def switch_to_community_plan(self):
    """切换到 Community Plan"""
    self.log_info("计划切换", "准备将当前账号切换到 Community Plan...")
    self.log_batch_operation("开始切换账号到 Community Plan")
    self.start_worker_thread("put_user_on_community_plan")
```

## API 调用详情

### 请求信息
- **URL**: `https://app.augmentcode.com/api/put-user-on-plan`
- **方法**: POST
- **请求头**: 使用配置文件中的标准请求头
- **请求体**: 
  ```json
  {
    "planId": "orb_community_plan"
  }
  ```

### 响应处理
- **成功**: HTTP 200 状态码，显示成功消息
- **失败**: 其他状态码，显示错误信息和响应内容
- **异常**: 网络错误等异常情况的处理

## 使用方法

1. **启动应用**: 运行 `team_manager.py`
2. **配置API**: 在"工具" -> "配置设置"中配置正确的Cookie和其他API信息
3. **切换标签页**: 点击"🔄 批量操作"标签页
4. **执行操作**: 点击"🔄 切换到 Community Plan"按钮
5. **查看结果**: 在系统日志中查看操作结果

## 安全注意事项

⚠️ **重要提醒**:
- 此操作会实际改变您的账号计划
- 操作不可撤销，请谨慎使用
- 确保您有足够的权限执行此操作
- 建议在操作前确认当前账号状态

## 日志记录

操作过程中会记录以下日志：
- 操作开始提示
- API调用结果（成功/失败）
- 详细的错误信息（如果失败）
- 操作完成状态

## 技术特性

- **异步执行**: 使用工作线程避免界面冻结
- **错误处理**: 完善的异常处理和错误提示
- **日志记录**: 详细的操作日志和状态跟踪
- **界面集成**: 与现有UI风格保持一致
- **配置支持**: 使用统一的配置管理系统
