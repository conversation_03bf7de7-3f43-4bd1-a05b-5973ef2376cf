#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队邀请工具 - 安装脚本
"""

from setuptools import setup, find_packages

setup(
    name="team_inviter",
    version="1.0.0",
    description="团队邀请工具，用于批量邀请成员和管理邀请记录",
    author="Augment Team",
    packages=find_packages(),
    install_requires=[
        "requests>=2.25.0",
    ],
    extras_require={
        "gui": ["PyQt6>=6.2.0"],
    },
    entry_points={
        "console_scripts": [
            "team-inviter=team_inviter.__main__:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
) 