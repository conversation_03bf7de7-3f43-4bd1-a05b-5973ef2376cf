#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队邀请工具 - 邀请模块
支持邀请成员、删除邀请等功能
"""

import json
import os
import re
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
import requests


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "team_inviter_config.json"):
        self.config_file = config_file
        self.default_config = {
            "api": {
                "base_url": "https://app.augmentcode.com/api",
                "headers": {
                    "accept": "*/*",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "cache-control": "no-cache",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
                    "cookie": "",
                    "Referer": "https://app.augmentcode.com/account/team",
                    "Referrer-Policy": "strict-origin-when-cross-origin"
                }
            },
            "features": {
                "batch_operations": True,
                "email_validation": True,
                "auto_save": True
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                return self._merge_config(self.default_config, config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, path: str, default=None):
        """获取配置值，支持点分隔路径"""
        keys = path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    
    def set(self, path: str, value):
        """设置配置值，支持点分隔路径"""
        keys = path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value


class APIClient:
    """API客户端类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = self.config.get('api.headers', {}).copy()
        # 为POST请求添加content-type
        if 'content-type' not in headers:
            headers['content-type'] = 'application/json'
        return headers
    
    def validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def get_team_data(self) -> Tuple[bool, Any]:
        """获取团队数据"""
        try:
            url = f"{self.config.get('api.base_url')}/team"
            headers = self._get_headers()
            
            response = self.session.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, f"请求失败，状态码: {response.status_code}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def invite_members(self, emails: List[str]) -> Tuple[bool, str]:
        """批量邀请成员"""
        try:
            url = f"{self.config.get('api.base_url')}/team/invite"
            headers = self._get_headers()
            data = {"emails": emails}
            
            response = self.session.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                return True, "邀请发送成功"
            else:
                return False, f"邀请失败，状态码: {response.status_code}\n响应: {response.text}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def delete_member(self, member_id: str) -> Tuple[bool, str]:
        """删除单个成员或邀请"""
        try:
            url = f"{self.config.get('api.base_url')}/team/invite/{member_id}"
            headers = self._get_headers()
            
            response = self.session.delete(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                return True, "删除成功"
            else:
                return False, f"删除失败，状态码: {response.status_code}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"


class TeamInviter:
    """团队邀请管理类"""
    
    def __init__(self, config_file: str = "team_inviter_config.json"):
        self.config = Config(config_file)
        self.api_client = APIClient(self.config)
        self.team_data = None
    
    def validate_emails(self, emails_text: str) -> Tuple[List[str], List[str]]:
        """验证邮箱列表，返回有效和无效的邮箱"""
        if not emails_text.strip():
            return [], []
        
        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        valid_emails = []
        invalid_emails = []
        
        for email in emails:
            if self.api_client.validate_email(email):
                valid_emails.append(email)
            else:
                invalid_emails.append(email)
        
        return valid_emails, invalid_emails
    
    def invite_members(self, emails: List[str]) -> Tuple[bool, str]:
        """邀请成员"""
        if not emails:
            return False, "没有有效的邮箱地址"
        
        return self.api_client.invite_members(emails)
    
    def get_team_data(self) -> Tuple[bool, Any]:
        """获取团队数据"""
        success, data = self.api_client.get_team_data()
        if success:
            self.team_data = data
        return success, data
    
    def extract_invitations_from_data(self, data) -> List[Dict]:
        """从数据中提取邀请列表"""
        invitations = []
        if isinstance(data, dict):
            for key, value in data.items():
                # 支持多种字段名：invitations, invites
                if key in ["invitations", "invites"] and isinstance(value, list):
                    invitations.extend(value)
                elif isinstance(value, (dict, list)):
                    invitations.extend(self.extract_invitations_from_data(value))
        elif isinstance(data, list):
            for item in data:
                invitations.extend(self.extract_invitations_from_data(item))
        return invitations
    
    def extract_users_from_data(self, data) -> List[Dict]:
        """从数据中提取用户列表"""
        users = []
        if isinstance(data, dict):
            for key, value in data.items():
                # 支持多种字段名：users, members
                if key in ["users", "members"] and isinstance(value, list):
                    users.extend(value)
                elif isinstance(value, (dict, list)):
                    users.extend(self.extract_users_from_data(value))
        elif isinstance(data, list):
            for item in data:
                users.extend(self.extract_users_from_data(item))
        return users
    
    def get_pending_invitations(self) -> List[Dict]:
        """获取所有待处理的邀请"""
        if not self.team_data:
            return []
        
        return self.extract_invitations_from_data(self.team_data)
    
    def get_pending_emails(self) -> List[str]:
        """获取所有待处理邀请的邮箱地址"""
        invitations = self.get_pending_invitations()
        return [inv.get('email', '') for inv in invitations if inv.get('email')]
    
    def get_invitation_ids(self) -> List[str]:
        """获取邀请记录的ID列表"""
        invitations = self.get_pending_invitations()
        return [inv.get('id', '') for inv in invitations if inv.get('id')]
    
    def batch_delete_invitations(self, invitation_ids: List[str]) -> Tuple[int, int, List[str]]:
        """批量删除邀请记录
        返回：成功数量，失败数量，失败的ID列表
        """
        if not invitation_ids:
            return 0, 0, []
        
        success_count = 0
        failed_count = 0
        failed_ids = []
        
        for member_id in invitation_ids:
            success, _ = self.api_client.delete_member(member_id)
            if success:
                success_count += 1
            else:
                failed_count += 1
                failed_ids.append(member_id)
        
        return success_count, failed_count, failed_ids
    
    def set_cookie(self, cookie: str):
        """设置API的Cookie"""
        self.config.set('api.headers.cookie', cookie)
        self.config.save_config()
    
    def set_base_url(self, base_url: str):
        """设置API的基础URL"""
        self.config.set('api.base_url', base_url)
        self.config.save_config()


if __name__ == "__main__":
    # 使用示例
    inviter = TeamInviter()
    
    # 设置Cookie（实际使用时需要替换为真实的Cookie）
    inviter.set_cookie("your_cookie_here")
    
    # 验证邮箱
    valid, invalid = inviter.validate_emails("<EMAIL>\ninvalid_email\<EMAIL>")
    print(f"有效邮箱: {valid}")
    print(f"无效邮箱: {invalid}")
    
    # 获取团队数据
    success, data = inviter.get_team_data()
    if success:
        print("团队数据获取成功")
        
        # 获取待处理邀请的邮箱
        pending_emails = inviter.get_pending_emails()
        print(f"待处理邀请邮箱: {pending_emails}")
    else:
        print(f"获取团队数据失败: {data}") 