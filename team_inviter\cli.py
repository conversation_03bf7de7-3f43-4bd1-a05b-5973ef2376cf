#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队邀请工具 - 命令行接口
提供简单的命令行操作邀请功能
"""

import argparse
import sys
import os
from typing import List, Optional
from datetime import datetime
from inviter import TeamInviter


def setup_parser() -> argparse.ArgumentParser:
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(description="团队邀请工具 - 命令行版")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 设置命令
    setup_parser = subparsers.add_parser("setup", help="设置API配置")
    setup_parser.add_argument("--cookie", help="设置API的Cookie")
    setup_parser.add_argument("--base-url", help="设置API的基础URL")

    # 验证邮箱命令
    validate_parser = subparsers.add_parser("validate", help="验证邮箱格式")
    validate_parser.add_argument("emails", nargs="+", help="要验证的邮箱地址")

    # 邀请命令
    invite_parser = subparsers.add_parser("invite", help="邀请成员")
    invite_group = invite_parser.add_mutually_exclusive_group(required=True)
    invite_group.add_argument("--emails", nargs="+", help="要邀请的邮箱地址")
    invite_group.add_argument("--file", help="包含邮箱地址的文件，每行一个邮箱")

    # 查询命令
    query_parser = subparsers.add_parser("query", help="查询团队数据")
    query_parser.add_argument("--pending", action="store_true", help="仅查询待处理的邀请")
    query_parser.add_argument("--output", help="将结果输出到文件")

    # 删除命令
    delete_parser = subparsers.add_parser("delete", help="删除邀请")
    delete_parser.add_argument("--id", help="要删除的邀请ID")
    delete_parser.add_argument("--all", action="store_true", help="删除所有待处理的邀请")

    return parser


def read_emails_from_file(file_path: str) -> List[str]:
    """从文件中读取邮箱地址"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            emails = [line.strip() for line in f if line.strip()]
        return emails
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []


def save_results_to_file(output_file: str, content: str):
    """将结果保存到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件失败: {e}")


def format_timestamp(timestamp):
    """格式化时间戳"""
    if not timestamp:
        return "未知"
    
    try:
        if isinstance(timestamp, (int, float)):
            return datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
        return str(timestamp)
    except:
        return str(timestamp)


def handle_setup(inviter: TeamInviter, args):
    """处理设置命令"""
    if args.cookie:
        inviter.set_cookie(args.cookie)
        print("Cookie 设置成功")
    
    if args.base_url:
        inviter.set_base_url(args.base_url)
        print("基础URL设置成功")


def handle_validate(inviter: TeamInviter, args):
    """处理验证邮箱命令"""
    emails = args.emails
    valid_emails = []
    invalid_emails = []
    
    for email in emails:
        if inviter.api_client.validate_email(email):
            valid_emails.append(email)
        else:
            invalid_emails.append(email)
    
    if valid_emails:
        print("有效邮箱:")
        for email in valid_emails:
            print(f"  ✓ {email}")
    
    if invalid_emails:
        print("无效邮箱:")
        for email in invalid_emails:
            print(f"  ✗ {email}")
    
    print(f"\n共 {len(emails)} 个邮箱，{len(valid_emails)} 个有效，{len(invalid_emails)} 个无效")


def handle_invite(inviter: TeamInviter, args):
    """处理邀请命令"""
    emails = []
    
    if args.emails:
        emails = args.emails
    elif args.file:
        emails = read_emails_from_file(args.file)
    
    if not emails:
        print("没有有效的邮箱地址")
        return
    
    # 验证邮箱
    valid_emails = []
    invalid_emails = []
    
    for email in emails:
        if inviter.api_client.validate_email(email):
            valid_emails.append(email)
        else:
            invalid_emails.append(email)
    
    if invalid_emails:
        print(f"警告: 发现 {len(invalid_emails)} 个无效邮箱，将被跳过")
        for email in invalid_emails[:5]:
            print(f"  ✗ {email}")
        if len(invalid_emails) > 5:
            print(f"  ... 还有 {len(invalid_emails) - 5} 个未显示")
    
    if not valid_emails:
        print("没有有效的邮箱地址，邀请取消")
        return
    
    print(f"准备邀请 {len(valid_emails)} 个有效邮箱...")
    success, message = inviter.invite_members(valid_emails)
    
    if success:
        print(f"✓ 邀请发送成功，已发送 {len(valid_emails)} 个邀请")
    else:
        print(f"✗ 邀请失败: {message}")


def handle_query(inviter: TeamInviter, args):
    """处理查询命令"""
    success, data = inviter.get_team_data()
    
    if not success:
        print(f"获取团队数据失败: {data}")
        return
    
    print("✓ 团队数据获取成功")
    
    if args.pending:
        invitations = inviter.get_pending_invitations()
        if not invitations:
            print("没有待处理的邀请")
            return
        
        print(f"\n找到 {len(invitations)} 个待处理邀请:")
        output = "待处理邀请列表:\n"
        output += "=" * 80 + "\n"
        output += f"{'ID':<40} {'邮箱':<30} {'邀请时间'}\n"
        output += "-" * 80 + "\n"
        
        for inv in invitations:
            inv_id = inv.get('id', 'N/A')
            email = inv.get('email', 'N/A')
            invited_at = format_timestamp(inv.get('invitedAt'))
            
            line = f"{inv_id:<40} {email:<30} {invited_at}"
            output += line + "\n"
            print(line)
        
        if args.output:
            save_results_to_file(args.output, output)
    else:
        users = inviter.extract_users_from_data(data)
        invitations = inviter.extract_invitations_from_data(data)
        
        print(f"\n团队成员: {len(users)} 人")
        print(f"待处理邀请: {len(invitations)} 个")
        
        if args.output:
            import json
            save_results_to_file(args.output, json.dumps(data, indent=2, ensure_ascii=False))


def handle_delete(inviter: TeamInviter, args):
    """处理删除命令"""
    if args.id:
        success, message = inviter.api_client.delete_member(args.id)
        if success:
            print(f"✓ 删除成功: {args.id}")
        else:
            print(f"✗ 删除失败: {message}")
    
    elif args.all:
        # 先获取团队数据
        success, data = inviter.get_team_data()
        
        if not success:
            print(f"获取团队数据失败: {data}")
            return
        
        invitation_ids = inviter.get_invitation_ids()
        
        if not invitation_ids:
            print("没有待处理的邀请可删除")
            return
        
        print(f"准备删除 {len(invitation_ids)} 个待处理邀请...")
        
        success_count, failed_count, failed_ids = inviter.batch_delete_invitations(invitation_ids)
        
        print(f"批量删除完成: 成功 {success_count} 个, 失败 {failed_count} 个")
        
        if failed_ids:
            print("失败的ID:")
            for id in failed_ids[:5]:
                print(f"  ✗ {id}")
            if len(failed_ids) > 5:
                print(f"  ... 还有 {len(failed_ids) - 5} 个未显示")
    else:
        print("错误: 必须指定 --id 或 --all 参数")


def main():
    """主函数"""
    parser = setup_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    inviter = TeamInviter()
    
    # 根据命令调用相应的处理函数
    if args.command == "setup":
        handle_setup(inviter, args)
    elif args.command == "validate":
        handle_validate(inviter, args)
    elif args.command == "invite":
        handle_invite(inviter, args)
    elif args.command == "query":
        handle_query(inviter, args)
    elif args.command == "delete":
        handle_delete(inviter, args)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"程序出错: {e}")
        sys.exit(1) 