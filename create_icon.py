#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建应用图标的脚本
"""

from PyQt6.QtGui import QPixmap, QPainter, QColor, QFont, QBrush, QPen
from PyQt6.QtCore import Qt, QRect
from PyQt6.QtWidgets import QApplication
import sys


def create_app_icon(size=64):
    """创建应用图标"""
    # 创建像素图
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # 创建画笔
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # 绘制背景圆形
    gradient_brush = QBrush(QColor("#007bff"))
    painter.setBrush(gradient_brush)
    painter.setPen(QPen(QColor("#0056b3"), 2))
    painter.drawEllipse(2, 2, size-4, size-4)
    
    # 绘制工具图标
    painter.setPen(QPen(QColor("white"), 3))
    
    # 绘制扳手
    center_x, center_y = size // 2, size // 2
    
    # 扳手主体
    painter.drawLine(center_x - 8, center_y - 8, center_x + 8, center_y + 8)
    
    # 扳手头部
    painter.drawLine(center_x - 10, center_y - 6, center_x - 6, center_y - 10)
    painter.drawLine(center_x + 6, center_y + 10, center_x + 10, center_y + 6)
    
    # 绘制螺丝刀
    painter.drawLine(center_x + 8, center_y - 8, center_x - 8, center_y + 8)
    
    # 螺丝刀头部
    painter.drawLine(center_x + 6, center_y - 10, center_x + 10, center_y - 6)
    painter.drawLine(center_x - 6, center_y + 10, center_x - 10, center_y + 6)
    
    painter.end()
    
    return pixmap


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建不同尺寸的图标
    sizes = [16, 24, 32, 48, 64, 128, 256]
    
    for size in sizes:
        icon = create_app_icon(size)
        filename = f"team_manager_icon_{size}x{size}.png"
        icon.save(filename, "PNG")
        print(f"创建图标: {filename}")
    
    # 创建默认图标
    default_icon = create_app_icon(64)
    default_icon.save("team_manager_icon.png", "PNG")
    print("创建默认图标: team_manager_icon.png")
    
    print("图标创建完成！")


if __name__ == "__main__":
    main()
