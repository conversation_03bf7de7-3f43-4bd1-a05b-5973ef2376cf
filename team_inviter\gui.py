#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队邀请工具 - 图形界面
提供图形化操作邀请功能
"""

import sys
import os
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QMessageBox, 
    QGroupBox, QFormLayout, QFileDialog, QTabWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QStatusBar
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from inviter import TeamInviter


class InviterGUI(QMainWindow):
    """邀请工具图形界面"""
    
    def __init__(self):
        super().__init__()
        self.inviter = TeamInviter()
        self.setWindowTitle("团队邀请工具")
        self.setGeometry(100, 100, 800, 600)
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 邀请标签页
        invite_tab = self.create_invite_tab()
        tab_widget.addTab(invite_tab, "邀请成员")
        
        # 查询标签页
        query_tab = self.create_query_tab()
        tab_widget.addTab(query_tab, "查询邀请")
        
        # 设置标签页
        settings_tab = self.create_settings_tab()
        tab_widget.addTab(settings_tab, "设置")
        
        main_layout.addWidget(tab_widget)
        
        # 状态栏
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("就绪")
        
        central_widget.setLayout(main_layout)
    
    def create_invite_tab(self):
        """创建邀请标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 邀请区域
        invite_group = QGroupBox("批量邀请")
        invite_layout = QVBoxLayout()
        
        # 邮箱输入区域
        email_label = QLabel("请输入邮箱地址（每行一个）:")
        invite_layout.addWidget(email_label)
        
        self.email_input = QTextEdit()
        self.email_input.setPlaceholderText("<EMAIL>\<EMAIL>\<EMAIL>")
        invite_layout.addWidget(self.email_input)
        
        # 验证结果显示
        self.validation_result = QLabel("验证结果: 请输入邮箱地址")
        invite_layout.addWidget(self.validation_result)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        validate_btn = QPushButton("验证邮箱")
        validate_btn.clicked.connect(self.validate_emails)
        
        invite_btn = QPushButton("发送邀请")
        invite_btn.clicked.connect(self.send_invites)
        
        load_file_btn = QPushButton("从文件加载")
        load_file_btn.clicked.connect(self.load_emails_from_file)
        
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_email_input)
        
        button_layout.addWidget(validate_btn)
        button_layout.addWidget(invite_btn)
        button_layout.addWidget(load_file_btn)
        button_layout.addWidget(clear_btn)
        
        invite_layout.addLayout(button_layout)
        invite_group.setLayout(invite_layout)
        layout.addWidget(invite_group)
        
        # 邀请历史区域
        history_group = QGroupBox("邀请历史")
        history_layout = QVBoxLayout()
        
        self.invite_history = QTextEdit()
        self.invite_history.setReadOnly(True)
        history_layout.addWidget(self.invite_history)
        
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_query_tab(self):
        """创建查询标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        load_data_btn = QPushButton("获取团队数据")
        load_data_btn.clicked.connect(self.load_team_data)
        
        refresh_btn = QPushButton("刷新数据")
        refresh_btn.clicked.connect(self.load_team_data)
        
        export_btn = QPushButton("导出数据")
        export_btn.clicked.connect(self.export_team_data)
        
        control_layout.addWidget(load_data_btn)
        control_layout.addWidget(refresh_btn)
        control_layout.addWidget(export_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 邀请表格
        self.invitations_table = QTableWidget()
        self.invitations_table.setColumnCount(3)
        self.invitations_table.setHorizontalHeaderLabels(["ID", "邮箱", "邀请时间"])
        self.invitations_table.horizontalHeader().setStretchLastSection(True)
        self.invitations_table.setAlternatingRowColors(True)
        self.invitations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.invitations_table.verticalHeader().setVisible(False)
        
        layout.addWidget(self.invitations_table)
        
        # 操作按钮区域
        action_layout = QHBoxLayout()
        
        delete_selected_btn = QPushButton("删除选中")
        delete_selected_btn.clicked.connect(self.delete_selected_invitation)
        
        delete_all_btn = QPushButton("删除全部")
        delete_all_btn.clicked.connect(self.delete_all_invitations)
        
        action_layout.addWidget(delete_selected_btn)
        action_layout.addWidget(delete_all_btn)
        action_layout.addStretch()
        
        layout.addLayout(action_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_settings_tab(self):
        """创建设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # API设置
        api_group = QGroupBox("API设置")
        api_layout = QFormLayout()
        
        # 基础URL
        self.base_url_input = QLineEdit(self.inviter.config.get("api.base_url", ""))
        api_layout.addRow("API基础URL:", self.base_url_input)
        
        # Cookie
        self.cookie_input = QTextEdit()
        self.cookie_input.setPlainText(self.inviter.config.get("api.headers.cookie", ""))
        self.cookie_input.setMaximumHeight(100)
        api_layout.addRow("Cookie:", self.cookie_input)
        
        # 设置按钮
        save_settings_btn = QPushButton("保存设置")
        save_settings_btn.clicked.connect(self.save_settings)
        api_layout.addRow("", save_settings_btn)
        
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)
        
        # 使用说明
        help_group = QGroupBox("使用说明")
        help_layout = QVBoxLayout()
        
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h3>团队邀请工具使用说明</h3>
        <p>这个工具用于管理团队邀请，支持以下功能：</p>
        <ul>
            <li><b>邀请成员</b>：批量邀请成员加入团队</li>
            <li><b>查询邀请</b>：查看和管理待处理的邀请</li>
            <li><b>删除邀请</b>：删除未接受的邀请</li>
        </ul>
        <p><b>注意</b>：使用前请先在设置页面配置API信息</p>
        """)
        help_layout.addWidget(help_text)
        
        help_group.setLayout(help_layout)
        layout.addWidget(help_group)
        
        widget.setLayout(layout)
        return widget
    
    # =============== 邀请功能 ===============
    
    def validate_emails(self):
        """验证邮箱地址"""
        emails_text = self.email_input.toPlainText().strip()
        if not emails_text:
            self.validation_result.setText("验证结果: 请输入邮箱地址")
            return
        
        valid_emails, invalid_emails = self.inviter.validate_emails(emails_text)
        
        result_text = f"验证结果: 共 {len(valid_emails) + len(invalid_emails)} 个邮箱, "
        result_text += f"{len(valid_emails)} 个有效, {len(invalid_emails)} 个无效"
        
        if invalid_emails:
            result_text += f"\n无效邮箱: {', '.join(invalid_emails[:5])}"
            if len(invalid_emails) > 5:
                result_text += f"... 等 {len(invalid_emails)} 个"
        
        self.validation_result.setText(result_text)
        self.statusbar.showMessage(f"验证完成: {len(valid_emails)} 个有效, {len(invalid_emails)} 个无效")
    
    def send_invites(self):
        """发送邀请"""
        emails_text = self.email_input.toPlainText().strip()
        if not emails_text:
            QMessageBox.warning(self, "提示", "请输入邮箱地址")
            return
        
        valid_emails, invalid_emails = self.inviter.validate_emails(emails_text)
        
        if not valid_emails:
            QMessageBox.warning(self, "提示", "没有有效的邮箱地址")
            return
        
        if invalid_emails:
            msg = f"发现 {len(invalid_emails)} 个无效邮箱，是否继续邀请 {len(valid_emails)} 个有效邮箱？"
            reply = QMessageBox.question(self, "确认", msg, 
                                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                        QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                return
        
        self.statusbar.showMessage(f"正在邀请 {len(valid_emails)} 个成员...")
        
        success, message = self.inviter.invite_members(valid_emails)
        
        if success:
            QMessageBox.information(self, "成功", f"成功发送 {len(valid_emails)} 个邀请")
            self.log_invite_history(f"成功发送 {len(valid_emails)} 个邀请")
            self.statusbar.showMessage(f"邀请成功: {len(valid_emails)} 个邮箱")
        else:
            QMessageBox.critical(self, "失败", f"邀请失败: {message}")
            self.log_invite_history(f"邀请失败: {message}")
            self.statusbar.showMessage("邀请失败")
    
    def load_emails_from_file(self):
        """从文件加载邮箱地址"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                emails = [line.strip() for line in f if line.strip()]
            
            if not emails:
                QMessageBox.warning(self, "提示", "文件中没有找到邮箱地址")
                return
            
            self.email_input.setPlainText("\n".join(emails))
            self.validate_emails()
            self.statusbar.showMessage(f"从文件加载了 {len(emails)} 个邮箱地址")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")
    
    def clear_email_input(self):
        """清空邮箱输入"""
        self.email_input.clear()
        self.validation_result.setText("验证结果: 请输入邮箱地址")
    
    def log_invite_history(self, message):
        """记录邀请历史"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.invite_history.append(f"[{timestamp}] {message}")
    
    # =============== 查询功能 ===============
    
    def load_team_data(self):
        """加载团队数据"""
        self.statusbar.showMessage("正在获取团队数据...")
        
        success, data = self.inviter.get_team_data()
        
        if success:
            invitations = self.inviter.get_pending_invitations()
            self.update_invitations_table(invitations)
            self.statusbar.showMessage(f"数据加载成功: 找到 {len(invitations)} 个待处理邀请")
        else:
            QMessageBox.critical(self, "错误", f"获取数据失败: {data}")
            self.statusbar.showMessage("数据加载失败")
    
    def update_invitations_table(self, invitations):
        """更新邀请表格"""
        self.invitations_table.setRowCount(len(invitations))
        
        for row, invitation in enumerate(invitations):
            # ID
            id_item = QTableWidgetItem(invitation.get('id', ''))
            self.invitations_table.setItem(row, 0, id_item)
            
            # 邮箱
            email_item = QTableWidgetItem(invitation.get('email', ''))
            self.invitations_table.setItem(row, 1, email_item)
            
            # 邀请时间
            invited_at = invitation.get('invitedAt', '')
            if invited_at:
                try:
                    if isinstance(invited_at, (int, float)):
                        invited_at = datetime.fromtimestamp(invited_at / 1000).strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
            time_item = QTableWidgetItem(str(invited_at))
            self.invitations_table.setItem(row, 2, time_item)
    
    def delete_selected_invitation(self):
        """删除选中的邀请"""
        selected_rows = self.invitations_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "提示", "请先选择要删除的邀请")
            return
        
        # 获取选中行的ID
        ids = []
        for row in selected_rows:
            id_item = self.invitations_table.item(row.row(), 0)
            if id_item:
                ids.append(id_item.text())
        
        if not ids:
            return
        
        # 确认删除
        reply = QMessageBox.question(self, "确认", f"确定要删除选中的 {len(ids)} 个邀请吗？", 
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                    QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.No:
            return
        
        # 执行删除
        success_count, failed_count, failed_ids = self.inviter.batch_delete_invitations(ids)
        
        if failed_count == 0:
            QMessageBox.information(self, "成功", f"成功删除 {success_count} 个邀请")
            self.load_team_data()  # 刷新数据
        else:
            QMessageBox.warning(self, "部分成功", 
                              f"删除结果: 成功 {success_count} 个, 失败 {failed_count} 个")
            self.load_team_data()  # 刷新数据
    
    def delete_all_invitations(self):
        """删除所有邀请"""
        if not self.inviter.team_data:
            QMessageBox.warning(self, "提示", "请先加载团队数据")
            return
        
        invitation_ids = self.inviter.get_invitation_ids()
        
        if not invitation_ids:
            QMessageBox.information(self, "提示", "没有待处理的邀请可删除")
            return
        
        # 确认删除
        reply = QMessageBox.question(self, "确认", f"确定要删除所有 {len(invitation_ids)} 个待处理邀请吗？",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                    QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.No:
            return
        
        self.statusbar.showMessage(f"正在删除 {len(invitation_ids)} 个邀请...")
        
        # 执行删除
        success_count, failed_count, failed_ids = self.inviter.batch_delete_invitations(invitation_ids)
        
        if failed_count == 0:
            QMessageBox.information(self, "成功", f"成功删除所有 {success_count} 个邀请")
            self.load_team_data()  # 刷新数据
        else:
            QMessageBox.warning(self, "部分成功", 
                              f"删除结果: 成功 {success_count} 个, 失败 {failed_count} 个")
            self.load_team_data()  # 刷新数据
    
    def export_team_data(self):
        """导出团队数据"""
        if not self.inviter.team_data:
            QMessageBox.warning(self, "提示", "请先加载团队数据")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", f"team_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if not file_path:
            return
        
        try:
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.inviter.team_data, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            self.statusbar.showMessage(f"数据导出成功: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    # =============== 设置功能 ===============
    
    def save_settings(self):
        """保存设置"""
        base_url = self.base_url_input.text().strip()
        cookie = self.cookie_input.toPlainText().strip()
        
        if not base_url:
            QMessageBox.warning(self, "提示", "请输入API基础URL")
            return
        
        if not cookie:
            QMessageBox.warning(self, "提示", "请输入Cookie")
            return
        
        self.inviter.set_base_url(base_url)
        self.inviter.set_cookie(cookie)
        
        QMessageBox.information(self, "成功", "设置已保存")
        self.statusbar.showMessage("设置已保存")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = InviterGUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 