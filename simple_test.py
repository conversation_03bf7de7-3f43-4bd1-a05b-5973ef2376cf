#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新功能
"""

print("🧪 测试开始")

try:
    from team_manager import Config, APIClient
    print("✅ 成功导入 team_manager 模块")
    
    # 创建配置
    config = Config()
    print("✅ 成功创建配置对象")
    
    # 创建API客户端
    api_client = APIClient(config)
    print("✅ 成功创建API客户端")
    
    # 检查新方法是否存在
    if hasattr(api_client, 'put_user_on_community_plan'):
        print("✅ 新方法 put_user_on_community_plan 存在")
        
        # 显示方法信息
        method = getattr(api_client, 'put_user_on_community_plan')
        print(f"📋 方法类型: {type(method)}")
        print(f"📋 方法文档: {method.__doc__}")
        
    else:
        print("❌ 新方法 put_user_on_community_plan 不存在")
    
    print("✅ 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()

print("🏁 测试结束")
