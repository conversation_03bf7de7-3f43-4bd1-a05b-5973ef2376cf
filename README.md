# 团队邀请工具 (Team Inviter)

一个简单易用的团队成员邀请管理工具，支持批量邀请成员和管理邀请记录。

## 功能特点

- 批量邀请成员加入团队
- 验证邮箱格式，确保邀请有效
- 查询和管理待处理的邀请
- 批量删除未接受的邀请
- 支持命令行和图形界面两种使用方式

## 安装方法

### 安装依赖

基本依赖：
```bash
pip install requests
```

如果需要使用图形界面，还需要安装：
```bash
pip install PyQt6
```

### 从源码安装

```bash
# 克隆仓库
git clone <repository-url>
cd team-inviter

# 安装（基本功能）
pip install .

# 安装（包含图形界面）
pip install .[gui]
```

## 使用方法

### 图形界面模式

```bash
# 直接运行
python -m team_inviter

# 或使用安装后的命令
team-inviter
```

### 命令行模式

```bash
# 使用命令行模式
python -m team_inviter --cli

# 或使用安装后的命令
team-inviter --cli
```

### 命令行示例

```bash
# 设置API配置
team-inviter --cli setup --cookie "your_cookie_here" --base-url "https://app.example.com/api"

# 验证邮箱
team-inviter --<NAME_EMAIL> <EMAIL>

# 邀请成员
team-inviter --cli invite --emails <EMAIL> <EMAIL>

# 从文件邀请成员
team-inviter --cli invite --file emails.txt

# 查询待处理邀请
team-inviter --cli query --pending

# 删除所有待处理邀请
team-inviter --cli delete --all
```

## 配置说明

程序会在首次运行时创建配置文件 `team_inviter_config.json`。您可以通过图形界面的"设置"标签页或命令行的 `setup` 命令修改配置。

最重要的配置项是：

1. API基础URL - 团队API的基础地址
2. Cookie - 用于身份验证的Cookie信息

## 注意事项

- 所有删除操作都是不可撤销的，请谨慎操作
- 批量邀请前建议先验证邮箱格式，确保邀请有效
- Cookie信息包含敏感数据，请妥善保管配置文件

## 开发者说明

### 项目结构

```
team_inviter/
├── __init__.py      # 包初始化文件
├── __main__.py      # 主入口
├── inviter.py       # 核心邀请功能
├── cli.py           # 命令行界面
└── gui.py           # 图形用户界面
```

### 扩展开发

如需扩展功能，建议从 `inviter.py` 中的 `TeamInviter` 类开始了解代码结构。
