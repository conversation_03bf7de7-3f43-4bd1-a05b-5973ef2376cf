#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试工具 - 用于测试API连接和数据获取
"""

import requests
import json
import sys

def test_api_connection():
    """测试API连接"""
    print("🔍 API连接调试工具")
    print("=" * 50)
    
    # 从配置文件读取设置
    try:
        with open('team_manager_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ 配置文件加载成功")
    except FileNotFoundError:
        print("❌ 配置文件不存在，请先运行主程序并配置API设置")
        return
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return
    
    # 获取API配置
    base_url = config.get('api', {}).get('base_url', 'https://app.augmentcode.com/api')
    headers = config.get('api', {}).get('headers', {})
    
    print(f"📡 API地址: {base_url}")
    print(f"🍪 Cookie长度: {len(headers.get('cookie', ''))}")
    
    if not headers.get('cookie'):
        print("❌ Cookie未配置，请在主程序中配置API设置")
        return
    
    # 测试API请求
    url = f"{base_url}/team"
    
    print(f"\n🚀 测试API请求: {url}")
    print("⏳ 请求中...")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ API请求成功！")
            
            try:
                data = response.json()
                print("\n📋 响应数据结构:")
                print_data_structure(data, level=0)
                
                # 分析数据
                analyze_team_data(data)
                
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                
        elif response.status_code == 401:
            print("❌ 认证失败 (401) - Cookie可能已过期")
            print("💡 请重新获取Cookie并更新配置")
            
        elif response.status_code == 403:
            print("❌ 权限不足 (403) - 可能没有访问权限")
            
        elif response.status_code == 404:
            print("❌ API端点不存在 (404) - 请检查URL")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 网络连接可能有问题")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误 - 无法连接到服务器")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def print_data_structure(data, level=0):
    """打印数据结构"""
    indent = "  " * level
    
    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                print(f"{indent}{key}: {type(value).__name__}")
                if isinstance(value, list) and value:
                    print(f"{indent}  └─ 数组长度: {len(value)}")
                    if isinstance(value[0], dict):
                        print(f"{indent}  └─ 元素类型: {type(value[0]).__name__}")
                        if level < 2:  # 限制递归深度
                            print_data_structure(value[0], level + 2)
                elif isinstance(value, dict) and level < 2:
                    print_data_structure(value, level + 1)
            else:
                print(f"{indent}{key}: {type(value).__name__} = {str(value)[:50]}")
    elif isinstance(data, list):
        print(f"{indent}数组长度: {len(data)}")
        if data and level < 2:
            print(f"{indent}第一个元素:")
            print_data_structure(data[0], level + 1)

def analyze_team_data(data):
    """分析团队数据"""
    print("\n🔍 数据分析:")
    
    # 查找用户数据
    users = extract_users(data)
    invitations = extract_invitations(data)
    
    print(f"👥 找到用户数据: {len(users)} 个")
    print(f"📨 找到邀请数据: {len(invitations)} 个")
    
    if users:
        print("\n👥 用户详情:")
        for i, user in enumerate(users[:3]):  # 只显示前3个
            print(f"  {i+1}. ID: {user.get('id', 'N/A')[:10]}...")
            print(f"     邮箱: {user.get('email', 'N/A')}")
            print(f"     角色: {user.get('role', 'N/A')}")
    
    if invitations:
        print("\n📨 邀请详情:")
        for i, invitation in enumerate(invitations[:3]):  # 只显示前3个
            print(f"  {i+1}. ID: {invitation.get('id', 'N/A')[:10]}...")
            print(f"     邮箱: {invitation.get('email', 'N/A')}")

def extract_users(data, path=""):
    """递归提取用户数据"""
    users = []
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "users" and isinstance(value, list):
                print(f"  ✅ 在 {current_path} 找到用户数组")
                users.extend(value)
            elif isinstance(value, (dict, list)):
                users.extend(extract_users(value, current_path))
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            users.extend(extract_users(item, f"{path}[{i}]"))
    
    return users

def extract_invitations(data, path=""):
    """递归提取邀请数据"""
    invitations = []
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "invitations" and isinstance(value, list):
                print(f"  ✅ 在 {current_path} 找到邀请数组")
                invitations.extend(value)
            elif isinstance(value, (dict, list)):
                invitations.extend(extract_invitations(value, current_path))
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            invitations.extend(extract_invitations(item, f"{path}[{i}]"))
    
    return invitations

def main():
    """主函数"""
    try:
        test_api_connection()
    except KeyboardInterrupt:
        print("\n👋 调试被用户取消")
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
