<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 团队用户信息管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            border-radius: 10px;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .progress {
            height: 25px;
            border-radius: 15px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .loading {
            display: none;
        }
        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="text-center mb-0">
                <i class="fas fa-users"></i> 团队用户信息管理
            </h1>
            <p class="text-center mb-0 mt-2">管理团队成员：邀请新成员、查看现有成员、批量删除等操作</p>
        </div>
    </div>

    <div class="container">
        <!-- 邀请功能区域 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-envelope"></i> 邀请新成员</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">在下方文本框中输入邮箱地址，每行一个邮箱，然后点击批量邀请按钮。</p>
                
                <div class="mb-3">
                    <label for="emailInput" class="form-label">邮箱地址（每行一个）</label>
                    <textarea 
                        class="form-control" 
                        id="emailInput" 
                        rows="6" 
                        placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                        oninput="validateEmails()">
                    </textarea>
                </div>

                <!-- 实时验证显示 -->
                <div id="emailStats" class="row mb-3" style="display: none;">
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-value" id="totalEmails">0</div>
                            <div class="metric-label">总邮箱数</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-value text-success" id="validEmails">0</div>
                            <div class="metric-label">有效邮箱 ✅</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-value text-danger" id="invalidEmails">0</div>
                            <div class="metric-label">无效邮箱 ❌</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100" onclick="inviteMembers()">
                            <i class="fas fa-paper-plane"></i> 批量邀请
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-secondary w-100" onclick="clearInput()">
                            <i class="fas fa-broom"></i> 清空输入
                        </button>
                    </div>
                </div>

                <!-- 邀请结果显示 -->
                <div id="inviteResult" class="mt-3"></div>
            </div>
        </div>

        <hr class="my-4">

        <!-- 团队成员管理 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users-cog"></i> 团队成员管理</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary mb-3" onclick="loadTeamData()">
                    <i class="fas fa-sync-alt"></i> 获取团队用户信息
                </button>

                <!-- 加载状态 -->
                <div id="loadingIndicator" class="text-center loading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据，请稍候...</p>
                </div>

                <!-- 数据显示区域 -->
                <div id="dataContainer" style="display: none;">
                    <!-- 团队成员表格 -->
                    <div id="usersSection">
                        <h6><i class="fas fa-users"></i> 团队成员（users）</h6>
                        <div class="table-responsive">
                            <table class="table table-striped" id="usersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>ID</th>
                                        <th>邮箱</th>
                                        <th>角色</th>
                                        <th>加入时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        
                        <!-- 批量删除未加入成员 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button class="btn btn-danger w-100" id="batchDeleteMembers" onclick="batchDeleteMembers()" disabled>
                                    <i class="fas fa-trash-alt"></i> 删除未加入成员（批量删除）
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-info w-100" onclick="loadTeamData()">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- 邀请记录表格 -->
                    <div id="invitationsSection">
                        <h6><i class="fas fa-envelope-open"></i> 邀请记录（invitations）</h6>
                        <div class="table-responsive">
                            <table class="table table-striped" id="invitationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>ID</th>
                                        <th>邮箱</th>
                                        <th>邀请时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        
                        <!-- 批量删除邀请记录 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button class="btn btn-danger w-100" id="batchDeleteInvitations" onclick="batchDeleteInvitations()" disabled>
                                    <i class="fas fa-trash-alt"></i> 删除所有邀请记录（批量删除）
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-info w-100" onclick="loadTeamData()">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作结果显示 -->
                <div id="operationResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
