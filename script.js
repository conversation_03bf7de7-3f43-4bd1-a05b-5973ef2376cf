// 全局变量
let teamData = null;

// API配置 - 请更新为您的实际cookie和session信息
const API_CONFIG = {
    baseUrl: 'https://app.augmentcode.com/api',
    headers: {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": "_ga=GA1.1.312356878.1748975637; ajs_user_id=7bd14b58-889e-4276-8a37-cf28f1bb6057; ajs_anonymous_id=b8372b40-d7b4-4014-a9ac-05713b9f9808; _gcl_au=1.1.68962327.1748975637.282875774.1748975899.1748975899; _ga_F6GPDJDCJY=GS2.1.s1748975636$o1$g1$t1748976063$j8$l0$h0; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7v042GUF5F8ha81Iv81hF3rNhpOik0SV1fEl4T8MUEg; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%227bd14b58-889e-4276-8a37-cf28f1bb6057%22%2C%22%24sesid%22%3A%5B1748976319878%2C%**********-1b72-714e-9452-4ddc368d0832%22%2C1748975885169%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Fapp.augmentcode.com%2Faccount%2Fsubscription%22%7D%7D",
        "Referer": "https://app.augmentcode.com/account/team",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
};

// 邮箱验证函数
function validateEmail(email) {
    const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return pattern.test(email);
}

// 实时验证邮箱
function validateEmails() {
    const emailInput = document.getElementById('emailInput');
    const emailStats = document.getElementById('emailStats');
    const totalEmails = document.getElementById('totalEmails');
    const validEmails = document.getElementById('validEmails');
    const invalidEmails = document.getElementById('invalidEmails');

    const input = emailInput.value.trim();
    
    if (!input) {
        emailStats.style.display = 'none';
        return;
    }

    const emails = input.split('\n').map(email => email.trim()).filter(email => email);
    
    if (emails.length === 0) {
        emailStats.style.display = 'none';
        return;
    }

    const validCount = emails.filter(email => validateEmail(email)).length;
    const invalidCount = emails.length - validCount;

    totalEmails.textContent = emails.length;
    validEmails.textContent = validCount;
    invalidEmails.textContent = invalidCount;

    emailStats.style.display = 'block';
}

// 清空输入
function clearInput() {
    document.getElementById('emailInput').value = '';
    document.getElementById('emailStats').style.display = 'none';
    document.getElementById('inviteResult').innerHTML = '';
}

// 显示消息
function showMessage(containerId, message, type = 'info') {
    const container = document.getElementById(containerId);
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// 邀请成员
async function inviteMembers() {
    const emailInput = document.getElementById('emailInput');
    const input = emailInput.value.trim();

    if (!input) {
        showMessage('inviteResult', '请输入邮箱地址', 'warning');
        return;
    }

    const emails = input.split('\n').map(email => email.trim()).filter(email => email);
    
    if (emails.length === 0) {
        showMessage('inviteResult', '请输入至少一个邮箱地址', 'warning');
        return;
    }

    // 验证邮箱
    const validEmails = emails.filter(email => validateEmail(email));
    const invalidEmails = emails.filter(email => !validateEmail(email));

    let message = '';
    
    if (invalidEmails.length > 0) {
        message += `<p><strong>❌ 发现 ${invalidEmails.length} 个无效邮箱格式:</strong></p><ul>`;
        invalidEmails.forEach(email => {
            message += `<li>${email}</li>`;
        });
        message += '</ul>';
    }

    if (validEmails.length === 0) {
        showMessage('inviteResult', message + '<p>没有找到有效的邮箱地址</p>', 'warning');
        return;
    }

    message += `<p><strong>✅ 准备邀请 ${validEmails.length} 个有效邮箱:</strong></p><ul>`;
    validEmails.forEach(email => {
        message += `<li>${email}</li>`;
    });
    message += '</ul>';

    showMessage('inviteResult', message + '<p><i class="fas fa-spinner fa-spin"></i> 正在发送邀请...</p>', 'info');

    try {
        const response = await fetch(`${API_CONFIG.baseUrl}/team/invite`, {
            method: 'POST',
            headers: {
                ...API_CONFIG.headers,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ emails: validEmails })
        });

        if (response.ok) {
            showMessage('inviteResult', `🎉 成功发送邀请给 ${validEmails.length} 个邮箱！`, 'success');
            // 自动刷新数据
            if (teamData) {
                setTimeout(() => loadTeamData(), 1000);
            }
        } else {
            const errorText = await response.text();
            showMessage('inviteResult', `❌ 邀请发送失败: ${errorText}`, 'error');
        }
    } catch (error) {
        showMessage('inviteResult', `❌ 网络错误: ${error.message}`, 'error');
    }
}

// 加载团队数据
async function loadTeamData() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const dataContainer = document.getElementById('dataContainer');
    
    loadingIndicator.style.display = 'block';
    dataContainer.style.display = 'none';

    try {
        const response = await fetch(`${API_CONFIG.baseUrl}/team`, {
            method: 'GET',
            headers: API_CONFIG.headers
        });

        if (response.ok) {
            teamData = await response.json();
            displayTeamData(teamData);
            dataContainer.style.display = 'block';
        } else {
            showMessage('operationResult', `请求失败，状态码: ${response.status}`, 'error');
        }
    } catch (error) {
        showMessage('operationResult', `网络错误: ${error.message}`, 'error');
    } finally {
        loadingIndicator.style.display = 'none';
    }
}

// 显示团队数据
function displayTeamData(data) {
    // 提取用户数据
    let users = [];
    if (Array.isArray(data)) {
        users = data;
    } else if (data && typeof data === 'object') {
        users = extractUsers(data);
    }

    // 显示用户表格
    displayUsersTable(users);

    // 提取邀请数据
    let invitations = [];
    if (data && typeof data === 'object') {
        if (data.invitations) {
            invitations = data.invitations;
        } else if (data.team && data.team.invitations) {
            invitations = data.team.invitations;
        }
    }

    // 显示邀请表格
    displayInvitationsTable(invitations);
}

// 递归提取用户数据
function extractUsers(obj) {
    let result = [];
    if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
            if (key === 'users' && Array.isArray(value)) {
                result = result.concat(value);
            } else if (typeof value === 'object') {
                result = result.concat(extractUsers(value));
            }
        }
    }
    return result;
}

// 显示用户表格
function displayUsersTable(users) {
    const tbody = document.querySelector('#usersTable tbody');
    const batchDeleteBtn = document.getElementById('batchDeleteMembers');
    
    tbody.innerHTML = '';
    
    let unjoined = 0;
    
    users.forEach((user, index) => {
        const row = tbody.insertRow();
        const isUnjoined = !user.role;
        
        if (isUnjoined) unjoined++;
        
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${user.id || ''}</td>
            <td>${user.email || ''}</td>
            <td>${user.role || '<span class="text-muted">未加入成员</span>'}</td>
            <td>${user.joinedAt || ''}</td>
            <td>
                ${isUnjoined ? 
                    `<button class="btn btn-sm btn-danger" onclick="deleteMember('${user.id}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>` : 
                    '<span class="text-muted">-</span>'
                }
            </td>
        `;
    });
    
    batchDeleteBtn.disabled = unjoined === 0;
    batchDeleteBtn.textContent = `删除未加入成员（${unjoined}个）`;
}

// 显示邀请表格
function displayInvitationsTable(invitations) {
    const tbody = document.querySelector('#invitationsTable tbody');
    const batchDeleteBtn = document.getElementById('batchDeleteInvitations');
    
    tbody.innerHTML = '';
    
    invitations.forEach((invitation, index) => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${invitation.id || ''}</td>
            <td>${invitation.email || ''}</td>
            <td>${invitation.invitedAt || ''}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteMember('${invitation.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
    });
    
    batchDeleteBtn.disabled = invitations.length === 0;
    batchDeleteBtn.textContent = `删除所有邀请记录（${invitations.length}条）`;
}

// 删除单个成员
async function deleteMember(memberId) {
    if (!confirm('确定要删除这个成员吗？')) {
        return;
    }

    try {
        const response = await fetch(`${API_CONFIG.baseUrl}/team/invite/${memberId}`, {
            method: 'DELETE',
            headers: API_CONFIG.headers
        });

        if (response.ok) {
            showMessage('operationResult', '✅ 删除成功！', 'success');
            // 刷新数据
            setTimeout(() => loadTeamData(), 500);
        } else {
            showMessage('operationResult', `❌ 删除失败，状态码: ${response.status}`, 'error');
        }
    } catch (error) {
        showMessage('operationResult', `❌ 网络错误: ${error.message}`, 'error');
    }
}

// 批量删除未加入成员
async function batchDeleteMembers() {
    if (!teamData) {
        showMessage('operationResult', '请先加载团队数据', 'warning');
        return;
    }

    const users = extractUsers(teamData);
    const unjoinedMembers = users.filter(user => !user.role);

    if (unjoinedMembers.length === 0) {
        showMessage('operationResult', '没有未加入的成员需要删除', 'info');
        return;
    }

    if (!confirm(`确定要删除 ${unjoinedMembers.length} 个未加入成员吗？`)) {
        return;
    }

    await batchDelete(unjoinedMembers.map(user => user.id), '未加入成员');
}

// 批量删除邀请记录
async function batchDeleteInvitations() {
    if (!teamData) {
        showMessage('operationResult', '请先加载团队数据', 'warning');
        return;
    }

    let invitations = [];
    if (teamData.invitations) {
        invitations = teamData.invitations;
    } else if (teamData.team && teamData.team.invitations) {
        invitations = teamData.team.invitations;
    }

    if (invitations.length === 0) {
        showMessage('operationResult', '没有邀请记录需要删除', 'info');
        return;
    }

    if (!confirm(`确定要删除 ${invitations.length} 条邀请记录吗？`)) {
        return;
    }

    await batchDelete(invitations.map(inv => inv.id), '邀请记录');
}

// 批量删除通用函数
async function batchDelete(ids, type) {
    const total = ids.length;
    let successCount = 0;
    let failedCount = 0;
    let failedIds = [];

    // 显示进度
    let progressHtml = `
        <div class="alert alert-info">
            <h6>🚀 开始执行批量删除${type}...</h6>
            <div class="progress mb-2">
                <div class="progress-bar" role="progressbar" style="width: 0%" id="deleteProgress"></div>
            </div>
            <div id="deleteStatus">准备删除 ${total} 个${type}...</div>
        </div>
    `;

    document.getElementById('operationResult').innerHTML = progressHtml;

    for (let i = 0; i < ids.length; i++) {
        const id = ids[i];
        const progress = ((i + 1) / total) * 100;

        // 更新进度
        document.getElementById('deleteProgress').style.width = `${progress}%`;
        document.getElementById('deleteStatus').textContent = `正在删除第 ${i + 1}/${total} 个${type} (ID: ${id})...`;

        try {
            const response = await fetch(`${API_CONFIG.baseUrl}/team/invite/${id}`, {
                method: 'DELETE',
                headers: API_CONFIG.headers
            });

            if (response.ok) {
                successCount++;
            } else {
                failedCount++;
                failedIds.push(id);
            }
        } catch (error) {
            failedCount++;
            failedIds.push(id);
        }

        // 添加小延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 显示结果
    let resultMessage = '';
    if (failedCount === 0) {
        resultMessage = `✅ 成功删除所有 ${successCount} 个${type}！`;
        showMessage('operationResult', resultMessage, 'success');
    } else {
        resultMessage = `⚠️ 删除完成：成功 ${successCount} 个，失败 ${failedCount} 个`;
        if (failedIds.length > 0) {
            resultMessage += `<br>删除失败的ID: ${failedIds.join(', ')}`;
        }
        showMessage('operationResult', resultMessage, 'warning');
    }

    // 自动刷新数据
    setTimeout(() => loadTeamData(), 1000);
}
