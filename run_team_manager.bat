@echo off
chcp 65001 >nul
title 团队管理工具启动器

echo ================================================
echo 🛠️  团队管理工具启动器
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

echo.
echo 🚀 正在启动团队管理工具...
echo.

REM 运行启动脚本
python run_team_manager.py

REM 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    pause
)

echo.
echo 👋 程序已退出
pause
